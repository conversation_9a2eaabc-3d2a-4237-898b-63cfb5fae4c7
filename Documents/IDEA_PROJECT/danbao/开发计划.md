# 担保申请模块开发计划

## 📋 **开发进度总览**

### ✅ **已完成**
- [x] 数据库设计与创建
- [x] 后端API开发（担保申请、合作银行）
- [x] 前端页面开发（列表、表单、资料管理）
- [x] 字典数据配置（申请状态、客户类型、资料类型等）
- [x] 日期时间处理标准化
- [x] 前端列表操作列固定右侧
- [x] 菜单权限配置（担保申请菜单及权限按钮）
- [x] 申请资料管理功能（上传、查看、删除）
- [x] 模拟数据补充（7条申请记录 + 对应资料）

### 🔄 **进行中**
- [x] 接口测试验证（基础CRUD已验证）
- [x] 前端功能完整性测试（担保申请模块）
- [x] 客户管理模块开发
  - [x] 数据库表设计（客户基本信息、企业详情、个人详情、客户资料）
  - [x] 字典数据配置（证件类型、客户来源、客户等级、风险等级）
  - [x] 菜单权限配置
  - [x] 模拟数据生成（7个客户，包含企业和个人）
  - [x] 后端开发（实体类、Mapper、Service、Controller、Convert、VO类）
  - [x] 前端开发（API接口、列表页面、表单页面、资料管理对话框）

### 📅 **待完成**
- [ ] 客户管理模块完成（后端Service、Controller、前端页面）
- [ ] 合作银行模块完善
- [ ] 用户权限测试
- [ ] 完整功能测试
- [ ] 性能优化

---

## 🎯 **开发标准规范**

### 📅 **日期时间处理标准**

#### **前端标准**
1. **表单日期选择器**
   ```vue
   <el-date-picker
     v-model="formData.applicationDate"
     type="date"
     value-format="YYYY-MM-DD"
     placeholder="选择申请日期"
   />
   ```
   - 使用 `type="date"` 仅选择日期
   - 使用 `value-format="YYYY-MM-DD"` 输出日期字符串格式
   - 占位符使用 `选择XXX日期` 格式

2. **列表日期显示**
   ```vue
   <el-table-column
     label="申请日期"
     align="center"
     prop="applicationDate"
     :formatter="dateFormatter2"
     width="180px"
   />
   ```
   - 使用 `dateFormatter2` 显示为 `YYYY-MM-DD` 格式
   - 使用 `dateFormatter` 显示为 `YYYY-MM-DD HH:mm:ss` 格式
   - 日期列宽度设置为 `180px`

3. **导入工具函数**
   ```typescript
   import { dateFormatter, dateFormatter2 } from '@/utils/formatTime'
   ```

#### **后端标准**
1. **实体类日期字段**
   ```java
   /**
    * 申请日期
    */
   private LocalDate applicationDate;
   ```
   - 仅日期使用 `LocalDate` 类型
   - 日期时间使用 `LocalDateTime` 类型

2. **数据库字段类型**
   ```sql
   `application_date` date COMMENT '申请日期',
   `create_time` datetime COMMENT '创建时间',
   ```
   - 仅日期使用 `date` 类型
   - 日期时间使用 `datetime` 类型

### 🎨 **前端界面标准**

#### **列表页面**
1. **操作列位置**
   - 操作列必须放在表格最右侧
   - 操作列宽度设置为 `200px`
   - 操作按钮使用 `link` 类型

2. **表格列顺序**
   ```
   编号 -> 名称 -> 类型 -> 金额 -> 期限 -> 用途 -> 日期 -> 状态 -> 创建时间 -> 操作
   ```

#### **表单页面**
1. **日期字段**
   - 必填字段添加验证规则
   - 使用统一的占位符格式
   - 表单宽度使用 `class="!w-1/1"`

---

## 🔧 **技术实现细节**

### **日期处理流程**
1. **前端 -> 后端**：日期字符串(YYYY-MM-DD) -> LocalDate
2. **后端 -> 前端**：LocalDate -> 日期字符串(YYYY-MM-DD)
3. **显示格式化**：日期字符串 -> YYYY-MM-DD

### **已修复的问题**
1. ✅ **申请日期回显问题**
   - 修改表单日期选择器：`type="date"` + `value-format="YYYY-MM-DD"`
   - 修改列表日期显示：使用 `dateFormatter2`
   - 修改后端字段类型：`LocalDateTime` -> `LocalDate`
   - 更新所有相关VO类的字段类型
   - 更新数据库测试数据为正确的日期格式

2. ✅ **操作列位置问题**
   - 确认操作列已在最右侧位置

---

## 📝 **开发注意事项**

### **日期时间开发规范**
- 所有新开发的日期字段都必须遵循此标准
- 表单中的日期选择器统一使用 `value-format="YYYY-MM-DD"`
- 列表中的日期显示统一使用 `dateFormatter2`
- 后端日期字段统一使用 `LocalDate` 类型
- 前后端数据传输使用日期字符串格式 `YYYY-MM-DD`

### **前端开发规范**
- 操作列必须在表格最右侧
- 日期列宽度统一为 `180px`
- 操作列宽度统一为 `200px`

---

## 🚀 **完整开发流程标准**

### **每个模块的完整开发流程（一次性完成）**

#### **阶段1：数据库设计**
- [x] 表结构设计（主表+关联表）
- [x] 字典数据配置
- [x] 菜单权限配置
- [x] 模拟数据生成
- [x] SQL脚本执行

#### **阶段2：后端开发（完整）**
- [x] DO实体类（所有相关表）
- [x] Mapper数据访问层（所有相关表）
- [x] VO类（PageReqVO、SaveReqVO、RespVO、ExportReqVO）
- [x] Convert转换器
- [x] Service接口和实现类
- [x] Controller控制器
- [x] 错误码定义
- [x] 后端编译测试

#### **阶段3：前端开发（完整）**
- [x] API接口文件（TypeScript）
- [x] 字典类型配置
- [x] 列表页面（搜索、分页、操作）
- [x] 表单页面（新增、编辑、验证）
- [x] 相关对话框组件
- [x] 前端运行测试

#### **阶段4：功能验证**
- [x] 编译测试通过
- [x] 功能完整性验证
- [x] 用户体验优化

### **后续模块开发顺序**
1. **客户管理模块** ✅（已完成）
2. **反担保物管理模块** ✅（已完成）
3. **费用管理模块** ✅（已完成）
4. **保后管理模块**（下一个优先级）
5. **综合管理模块**
